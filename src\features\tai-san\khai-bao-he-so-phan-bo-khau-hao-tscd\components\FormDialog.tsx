import { useState } from 'react';
import { BottomBar } from '@/components/custom/arito/form/bottom-bar';
import { HeSoPhanBoTSCDInput, HeSoPhanBoTSCD } from '@/types/schemas';

import { useForm } from 'react-hook-form';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoForm } from '@/components/custom/arito/form';
import { AritoIcon } from '@/components/custom/arito';
import { useSearchFieldStates } from '../hooks';
import { FormSchema } from '../schema';
import BasicInfo from './BasicInfo';

interface FormDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: HeSoPhanBoTSCDInput) => void;
  formMode: 'add' | 'edit' | 'view';
  initialData?: HeSoPhanBoTSCD;
  onAddButtonClick?: () => void;
  onEditButtonClick?: () => void;
  onDeleteButtonClick?: () => void;
  onCopyButtonClick?: () => void;
}

const FormDialog = ({
  open,
  onClose,
  onSubmit,
  formMode,
  initialData,
  onAddButtonClick,
  onEditButtonClick,
  onDeleteButtonClick,
  onCopyButtonClick
}: FormDialogProps) => {
  const [error, setError] = useState<string | null>(null);
  const methods = useForm({
    defaultValues: {
      he_so: initialData?.he_so || 0
    }
  });

  const {
    taiSan,
    setTaiSan,
    tkKhauHao,
    setTkKhauHao,
    tkChiPhi,
    setTkChiPhi,
    boPhanTS,
    setBoPhanTS,
    boPhan,
    setBoPhan,
    sanPham,
    setSanPham,
    vuViec,
    setVuViec,
    phi,
    setPhi,
    hopDong,
    setHopDong
  } = useSearchFieldStates(initialData);

  // This function is called when the form is submitted
  const handleFormSubmit = async (data: any) => {
    console.log('Form submitted with data:', data);
    try {
      const updatedData: HeSoPhanBoTSCDInput = {
        ma_ts: taiSan?.uuid || '',
        tk_kh: tkKhauHao?.uuid || '',
        tk_cp: tkChiPhi?.uuid || '',
        ma_bp_ts: boPhanTS?.uuid || '',
        ma_bp: boPhan?.uuid || '',
        ma_vv: vuViec?.uuid || '',
        ma_sp: sanPham?.uuid || '',
        ma_phi: phi?.uuid || '',
        ma_hd: hopDong?.uuid || '',
        he_so: data.he_so || 0,
        ky: 0,
        nam: 0
      };

      console.log('updatedData', updatedData);
      onSubmit(updatedData);
    } catch (err: any) {
      setError(err.message || 'Có lỗi xảy ra');
    }
  };

  const title =
    formMode === 'add'
      ? 'Thêm cập nhật'
      : formMode === 'edit'
        ? 'Sửa cập nhật'
        : 'Khai báo hệ số phân bổ khấu hao TSCĐ';

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title={title}
      titleIcon={<AritoIcon icon={281} />}
      maxWidth='xl'
      disableBackdropClose={true}
      disableEscapeKeyDown={true}
    >
      <AritoForm
        mode={formMode}
        hasAritoActionBar={false}
        schema={FormSchema}
        onSubmit={handleFormSubmit}
        initialData={
          formMode === 'add' && initialData
            ? {
                ...initialData,
                uuid: undefined, // Xóa uuid để tạo bản ghi mới
                ma_ts: taiSan?.uuid || '',
                tk_kh: tkKhauHao?.uuid || '',
                tk_cp: tkChiPhi?.uuid || '',
                ma_bp_ts: boPhanTS?.uuid || '',
                ma_bp: boPhan?.uuid || '',
                ma_vv: vuViec?.uuid || '',
                ma_sp: sanPham?.uuid || '',
                ma_phi: phi?.uuid || '',
                ma_hd: hopDong?.uuid || ''
              }
            : initialData
        }
        className='min-w-[50vw]'
        headerFields={
          <BasicInfo
            formMode={formMode}
            taiSan={taiSan}
            setTaiSan={setTaiSan}
            tkKhauHao={tkKhauHao}
            setTkKhauHao={setTkKhauHao}
            tkChiPhi={tkChiPhi}
            setTkChiPhi={setTkChiPhi}
            boPhanTS={boPhanTS}
            setBoPhanTS={setBoPhanTS}
            boPhan={boPhan}
            setBoPhan={setBoPhan}
            sanPham={sanPham}
            setSanPham={setSanPham}
            vuViec={vuViec}
            setVuViec={setVuViec}
            phi={phi}
            setPhi={setPhi}
            hopDong={hopDong}
            setHopDong={setHopDong}
          />
        }
        classNameBottomBar='relative w-full flex justify-end gap-2'
        bottomBar={
          <BottomBar
            mode={formMode}
            onAdd={onAddButtonClick}
            onEdit={onEditButtonClick}
            onDelete={onDeleteButtonClick}
            onCopy={onCopyButtonClick}
            onClose={onClose}
          />
        }
      />
      {error && <div className='mx-4 mb-4 rounded bg-red-100 p-2 text-red-700'>{error}</div>}
    </AritoDialog>
  );
};

export default FormDialog;
