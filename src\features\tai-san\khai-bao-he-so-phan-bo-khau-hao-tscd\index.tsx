'use client';

import { useState } from 'react';

import { ActionBar, FormDialog, DeleteDialog, SearchDialog } from './components';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { LoadingOverlay } from '@/components/custom/arito';
import { useRowSelection } from './hooks/useRowSelection';
import { useFormState, useHeSoPhanBoTSCD } from './hooks';
import { getDataTableColumns } from './cols-definition';
import type { HeSoPhanBoTSCDInput } from '@/types/schemas';
export default function KhaiBaoHeSoPhanBoKhauHaoTSCD() {
  const [searchData, setSearchData] = useState<any>(null);
  const [showTable, setShowTable] = useState(true);
  const { doiTuongList, isLoading, addDoiTuong, updateDoiTuong, deleteDoiTuong, refreshDoiTuongList } =
    useHeSoPhanBoTSCD(searchData);

  const {
    showForm,
    showDelete,
    formMode,
    isCopyMode,
    showSearchForm,
    handleCloseSearchForm,
    handleSearchClick,

    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick
  } = useFormState();

  const { selectedObj, selectedRowIndex, handleRowClick, clearSelection } = useRowSelection();

  const handleFormSubmit = async (data: HeSoPhanBoTSCDInput) => {
    try {
      const payload = {
        ...data,
        ky: searchData?.ky,
        nam: searchData?.nam,
      };

      if (formMode === 'add') {
        await addDoiTuong(payload);
      } else if (formMode === 'edit' && selectedObj) {
        await updateDoiTuong(selectedObj.uuid, payload);
      }

      handleCloseForm();
      clearSelection();
      // Refresh the list after successful submission
      await refreshDoiTuongList();
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  const displayData = doiTuongList?.filter(
    item => item.ky === Number(searchData?.ky) && item.nam === Number(searchData?.nam)
  );

  const tables = [
    {
      name: '',
      rows: displayData,
      columns: getDataTableColumns()
    }
  ];

  if (showSearchForm) {
    return (
      <SearchDialog
        open={showSearchForm}
        onClose={() => {
          setShowTable(false);
          handleCloseSearchForm();
        }}
        onSubmit={data => {
          setSearchData(data);
          handleCloseSearchForm();
        }}
      />
    );
  }

  return (
    <div className='flex h-screen flex-col lg:overflow-hidden'>
      <div className='w-full'>
        <ActionBar
          onAddClick={handleAddClick}
          onEditClick={() => selectedObj && handleEditClick()}
          onDeleteClick={() => selectedObj && handleDeleteClick()}
          onCopyClick={() => selectedObj && handleCopyClick()}
          onViewClick={() => selectedObj && handleViewClick()}
          onRefreshClick={() => refreshDoiTuongList()}
          onSearchClick={handleSearchClick}
          searchData={searchData}
        />

        {isLoading && <LoadingOverlay />}

        {!isLoading && showTable && (
          <AritoDataTables tables={tables} selectedRowId={selectedRowIndex || undefined} onRowClick={handleRowClick} />
        )}
      </div>

      {showForm && (
        <FormDialog
          open={showForm}
          onClose={handleCloseForm}
          onSubmit={handleFormSubmit}
          formMode={formMode}
          initialData={
            selectedObj && (formMode === 'edit' || formMode === 'view' || (formMode === 'add' && isCopyMode))
              ? selectedObj
              : formMode === 'add' && !isCopyMode
                ? undefined
                : undefined
          }
          onAddButtonClick={() => {
            handleCloseForm();
            clearSelection();
            handleAddClick();
          }}
          onEditButtonClick={() => {
            if (selectedObj) {
              handleCloseForm();
              handleEditClick();
            }
          }}
          onDeleteButtonClick={() => {
            if (selectedObj) {
              handleCloseForm();
              handleDeleteClick();
            }
          }}
          onCopyButtonClick={() => {
            if (selectedObj) {
              handleCloseForm();
              handleCopyClick();
            }
          }}
        />
      )}

      {showDelete && (
        <DeleteDialog
          open={showDelete}
          onClose={handleCloseDelete}
          selectedObj={selectedObj}
          deleteDoiTuong={deleteDoiTuong}
          clearSelection={clearSelection}
        />
      )}
    </div>
  );
}
