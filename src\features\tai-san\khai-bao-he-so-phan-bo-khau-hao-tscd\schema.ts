import { z } from 'zod';

export const FormSchema = z.object({
  ma_ts: z.string().optional(),
  tk_kh: z.string().optional(),
  tk_cp: z.string().optional(),
  ma_bp_ts: z.string().optional(),
  ma_bp: z.string().optional(),
  ma_vv: z.string().optional(),
  ma_sp: z.string().optional(),
  ma_phi: z.string().optional(),
  ma_hd: z.string().optional(),
  he_so: z.coerce.number().optional()
});

export type HeSoPhanBoTSCDSchemaType = z.infer<typeof FormSchema>;

export const initialValues: HeSoPhanBoTSCDSchemaType = {
  ma_ts: '',
  tk_kh: '',
  tk_cp: '',
  ma_bp_ts: '',
  ma_bp: '',
  ma_vv: '',
  ma_sp: '',
  ma_phi: '',
  ma_hd: '',
  he_so: 0
};
